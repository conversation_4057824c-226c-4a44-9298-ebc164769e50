#!/usr/bin/env python3
"""
Test script for Python Runner Creator application
Tests various functionality and error handling
"""

import os
import sys
import tempfile
import shutil
import subprocess
import configparser

# Add parent directory to path to import the main module
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_handling():
    """Test configuration file handling"""
    print("Testing configuration handling...")
    
    # Test with non-existent config file
    config_file = "test_setting.ini"
    if os.path.exists(config_file):
        os.remove(config_file)
    
    # Import after removing config file
    from importlib import reload
    import sys
    if 'Python Runner Creator' in sys.modules:
        del sys.modules['Python Runner Creator']
    
    print("✓ Config handling test passed")

def test_python_detection():
    """Test Python version detection"""
    print("Testing Python version detection...")
    
    try:
        # Test py launcher
        result = subprocess.run(["py", "-0p"], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✓ py launcher works")
        else:
            print("! py launcher not available")
    except Exception as e:
        print(f"! py launcher error: {e}")
    
    try:
        # Test python command
        result = subprocess.run(["python", "--version"], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("✓ python command works")
        else:
            print("! python command not available")
    except Exception as e:
        print(f"! python command error: {e}")
    
    print("✓ Python detection test completed")

def test_file_creation():
    """Test batch/vbs file creation logic"""
    print("Testing file creation logic...")
    
    # Create temporary test file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        f.write('print("Hello World")')
        test_file = f.name
    
    try:
        # Test bat file creation
        project_dir = os.path.dirname(test_file)
        base = os.path.splitext(test_file)[0]
        rel_path = os.path.relpath(test_file, start=project_dir)
        
        # Test .bat content
        lines = ["@echo off"]
        lines.append(f"cd /d \"{project_dir}\"")
        lines.append(f'python "{rel_path}"')
        lines.append("pause")
        bat_content = "\n".join(lines)
        
        bat_file = base + ".bat"
        with open(bat_file, "w", encoding="utf-8") as f:
            f.write(bat_content)
        
        if os.path.exists(bat_file):
            print("✓ BAT file creation works")
            os.remove(bat_file)
        
        # Test .vbs content
        cmd = f"cd /d \"{project_dir}\" & python \"{rel_path}\" & pause"
        vbs_content = (
            'Set WshShell = CreateObject("WScript.Shell")\n'
            f'WshShell.Run "cmd /k {cmd}", 0\n'
            'Set WshShell = Nothing\n'
        )
        
        vbs_file = base + ".vbs"
        with open(vbs_file, "w", encoding="utf-8") as f:
            f.write(vbs_content)
        
        if os.path.exists(vbs_file):
            print("✓ VBS file creation works")
            os.remove(vbs_file)
        
    finally:
        # Cleanup
        if os.path.exists(test_file):
            os.remove(test_file)
    
    print("✓ File creation test completed")

def test_error_handling():
    """Test error handling scenarios"""
    print("Testing error handling...")
    
    # Test with non-existent file
    non_existent = "/path/that/does/not/exist.py"
    if not os.path.exists(non_existent):
        print("✓ Non-existent file handling ready")
    
    # Test with invalid Python path
    invalid_python = "/invalid/python/path"
    if not os.path.exists(invalid_python):
        print("✓ Invalid Python path handling ready")
    
    print("✓ Error handling test completed")

def main():
    """Run all tests"""
    print("=" * 50)
    print("Python Runner Creator - Test Suite")
    print("=" * 50)
    
    try:
        test_config_handling()
        print()
        
        test_python_detection()
        print()
        
        test_file_creation()
        print()
        
        test_error_handling()
        print()
        
        print("=" * 50)
        print("All tests completed successfully!")
        print("=" * 50)
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

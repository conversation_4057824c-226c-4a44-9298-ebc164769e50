#!/usr/bin/env python3
"""
Basic GUI test for Python Runner Creator
Tests that the GUI can be created and basic functionality works
"""

import os
import sys
import tkinter as tk
import tempfile

# Add parent directory to path to import the main module
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_gui_creation():
    """Test that GUI can be created without errors"""
    print("Testing GUI creation...")
    
    try:
        # Import the main module
        import importlib.util
        spec = importlib.util.spec_from_file_location("main", "Python Runner Creator.py")
        main_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_module)
        
        # Create root window
        root = tk.Tk()
        root.withdraw()  # Hide the window for testing
        
        # Create app instance
        app = main_module.BatchFileCreatorApp(root)
        
        # Test basic functionality
        print("✓ GUI created successfully")
        
        # Test setting file path
        test_file = __file__  # Use this test file
        app.file_path.set(test_file)
        print(f"✓ File path set: {app.file_path.get()}")
        
        # Test settings
        app.add_pause.set(True)
        app.auto_exit.set(False)
        app.file_type.set("bat")
        app.use_venv.set(False)
        print("✓ Settings configured")
        
        # Test save settings
        app.save_settings()
        print("✓ Settings saved")
        
        # Test load settings
        app.load_settings()
        print("✓ Settings loaded")
        
        # Cleanup
        root.destroy()
        print("✓ GUI cleanup completed")
        
        return True
        
    except Exception as e:
        print(f"✗ GUI test failed: {e}")
        return False

def test_file_creation_logic():
    """Test file creation without actually creating files"""
    print("Testing file creation logic...")
    
    try:
        # Create a temporary Python file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write('print("Test file")')
            test_file = f.name
        
        # Test path validation
        if os.path.exists(test_file):
            print("✓ Test file exists")
        
        # Test directory extraction
        project_dir = os.path.dirname(test_file)
        base = os.path.splitext(test_file)[0]
        rel_path = os.path.relpath(test_file, start=project_dir)
        
        print(f"✓ Project dir: {project_dir}")
        print(f"✓ Base name: {base}")
        print(f"✓ Relative path: {rel_path}")
        
        # Cleanup
        os.remove(test_file)
        print("✓ File creation logic test completed")
        
        return True
        
    except Exception as e:
        print(f"✗ File creation logic test failed: {e}")
        return False

def main():
    """Run GUI tests"""
    print("=" * 50)
    print("Python Runner Creator - GUI Test Suite")
    print("=" * 50)
    
    success = True
    
    try:
        if not test_gui_creation():
            success = False
        print()
        
        if not test_file_creation_logic():
            success = False
        print()
        
        if success:
            print("=" * 50)
            print("All GUI tests completed successfully!")
            print("=" * 50)
        else:
            print("=" * 50)
            print("Some tests failed!")
            print("=" * 50)
        
    except Exception as e:
        print(f"Test suite failed with error: {e}")
        success = False
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

import tkinter as tk
from tkinter import filedialog, ttk, messagebox
import subprocess
import os
import sys
import configparser

CONFIG_FILE = "setting.ini"

class BatchFileCreatorApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Python Batch Runner Tool")
        self.root.geometry("500x360")
        self.center_window()

        self.file_path = tk.StringVar(value=os.getcwd())
        self.python_versions = []
        self.selected_python = tk.StringVar()

        self.add_pause = tk.BooleanVar()
        self.auto_exit = tk.BooleanVar()
        self.file_type = tk.StringVar()
        self.use_venv = tk.BooleanVar()

        self.create_widgets()
        self.load_settings()
        self.load_python_versions()

    def center_window(self):
        self.root.update_idletasks()
        w = self.root.winfo_width()
        h = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (w // 2)
        y = (self.root.winfo_screenheight() // 2) - (h // 2)
        self.root.geometry(f'{w}x{h}+{x}+{y}')

    def load_settings(self):
        config = configparser.ConfigParser()
        config.read(CONFIG_FILE)

        pause = True
        auto_exit = False
        file_type = "vbs"
        use_venv = False

        if config.has_section("Defaults"):
            pause = config.getboolean("Defaults", "pause", fallback=True)
            auto_exit = config.getboolean("Defaults", "auto_exit", fallback=False)
            file_type = config.get("Defaults", "file_type", fallback="vbs")
            use_venv = config.getboolean("Defaults", "use_venv", fallback=False)

        self.add_pause.set(pause)
        self.auto_exit.set(auto_exit)
        self.file_type.set(file_type)
        self.use_venv.set(use_venv)

    def save_settings(self):
        config = configparser.ConfigParser()
        config["Defaults"] = {
            "pause": str(self.add_pause.get()).lower(),
            "auto_exit": str(self.auto_exit.get()).lower(),
            "file_type": self.file_type.get(),
            "use_venv": str(self.use_venv.get()).lower()
        }
        with open(CONFIG_FILE, "w") as f:
            config.write(f)

    def create_widgets(self):
        frame = ttk.Frame(self.root, padding=10)
        frame.pack(fill="both", expand=True)

        ttk.Label(frame, text="🔍 Chọn file bất kỳ (.py, .txt, ...):").pack(anchor="w")
        file_frame = ttk.Frame(frame)
        file_frame.pack(fill="x", pady=5)
        ttk.Entry(file_frame, textvariable=self.file_path, width=50).pack(side="left", fill="x", expand=True)
        ttk.Button(file_frame, text="Chọn...", command=self.browse_file).pack(side="left", padx=5)

        ttk.Label(frame, text="🐍 Chọn phiên bản Python:").pack(anchor="w", pady=(10, 0))
        self.python_combo = ttk.Combobox(frame, textvariable=self.selected_python, state="readonly")
        self.python_combo.pack(fill="x", pady=5)

        ttk.Label(frame, text="📁 Chọn định dạng file tạo ra:").pack(anchor="w", pady=(10, 0))
        type_frame = ttk.Frame(frame)
        type_frame.pack(anchor="w", pady=3)
        ttk.Radiobutton(type_frame, text=".bat", variable=self.file_type, value="bat", command=self.save_settings).pack(side="left", padx=(0, 10))
        ttk.Radiobutton(type_frame, text=".vbs", variable=self.file_type, value="vbs", command=self.save_settings).pack(side="left")

        ttk.Checkbutton(frame, text="Thêm 'pause' vào cuối file batch", variable=self.add_pause, command=self.save_settings).pack(anchor="w", pady=3)
        ttk.Checkbutton(frame, text="Tạo file xong thì thoát luôn", variable=self.auto_exit, command=self.save_settings).pack(anchor="w", pady=3)
        ttk.Checkbutton(frame, text="Kích hoạt virtualenv (venv) nếu có", variable=self.use_venv, command=self.toggle_venv_mode).pack(anchor="w", pady=3)

        ttk.Button(frame, text="🚀 Tạo file chạy", command=self.create_launcher_file).pack(pady=10)

    def toggle_venv_mode(self):
        self.save_settings()
        self.python_combo.configure(state="disabled" if self.use_venv.get() else "readonly")

    def browse_file(self):
        file = filedialog.askopenfilename(title="Chọn file", filetypes=[("Tất cả các file", "*.*")])
        if file:
            self.file_path.set(file)

    def load_python_versions(self):
        if self.use_venv.get():
            return
        try:
            result = subprocess.run(["py", "-0p"], capture_output=True, text=True, check=True)
            lines = result.stdout.strip().splitlines()
            for line in lines:
                parts = line.strip().split()
                if parts[0].startswith("-V:"):
                    version = parts[0].replace("-V:", "")
                    path = parts[2] if "*" in parts else parts[1]
                    self.python_versions.append((version, path))
            if self.python_versions:
                self.python_combo["values"] = [f"{v} ({p})" for v, p in self.python_versions]
                self.selected_python.set(self.python_combo["values"][0])
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể lấy danh sách Python:\n{e}")

    def create_launcher_file(self):
        filepath = self.file_path.get()
        if not filepath or not os.path.exists(filepath):
            messagebox.showerror("Lỗi", "Vui lòng chọn file hợp lệ.")
            return

        project_dir = os.path.dirname(filepath)
        base = os.path.splitext(filepath)[0]
        file_type = self.file_type.get()

        try:
            if self.use_venv.get():
                python_cmd = "python"
                activate_cmd = os.path.join(project_dir, "venv", "Scripts", "activate.bat")
                activate_cmd = activate_cmd.replace("/", "\\")
            else:
                selected = self.selected_python.get()
                if not selected:
                    messagebox.showerror("Lỗi", "Vui lòng chọn phiên bản Python.")
                    return
                python_cmd = selected.split("(")[-1].strip(")")
                activate_cmd = None

            rel_path = os.path.relpath(filepath, start=project_dir)

            if file_type == "bat":
                output_file = base + ".bat"
                lines = []
                if activate_cmd and os.path.exists(activate_cmd):
                    lines.append(f'call "{activate_cmd}"')
                lines.append(f'{python_cmd} "{rel_path}"')
                if self.add_pause.get():
                    lines.append("pause")
                content = "\n".join(lines)
            else:
                cmd = ""
                if activate_cmd and os.path.exists(activate_cmd):
                    cmd += f'call "{activate_cmd}" & '
                cmd += f'{python_cmd} "{rel_path}"'
                content = (
                    'Set WshShell = CreateObject("WScript.Shell")\n'
                    f'WshShell.Run "cmd /k {cmd}", 0\n'
                    'Set WshShell = Nothing\n'
                )
                output_file = base + ".vbs"

            with open(output_file, "w", encoding="utf-8") as f:
                f.write(content)

            if self.auto_exit.get():
                self.root.destroy()
                sys.exit(0)
            else:
                messagebox.showinfo("✅ Thành công", f"Đã tạo file: {output_file}")
        except Exception as e:
            messagebox.showerror("Lỗi", f"Không thể tạo file:\n{e}")

if __name__ == "__main__":
    root = tk.Tk()
    app = BatchFileCreatorApp(root)
    root.mainloop()
